import { Q } from '@nozbe/watermelondb';
import database from '../database';
import Number from '../models/Number';

export interface NumberData {
  id?: string; // WatermelonDB ID (string)
  api_number_id?: number; // Original API numberId (number)
  company_id: number; // API companyId
  number: string;
  description?: string;
  type: string; // TOLL_FREE, ALL_INDIA, INTERNATIONAL
  upvote_count?: number;
  downvote_count?: number;
  is_whatsapp?: number; // 0 or 1
  created_at?: Date;
  updated_at?: Date;
}

class WatermelonNumberRepository {
  private collection = database.get<Number>('numbers');

  async getAll(): Promise<NumberData[]> {
    try {
      const numbers = await this.collection.query().fetch();
      return numbers.map(number => ({
        id: number.id,
        api_number_id: number.apiNumberId,
        company_id: number.companyId,
        number: number.number,
        description: number.description,
        type: number.type,
        upvote_count: number.upvoteCount,
        downvote_count: number.downvoteCount,
        is_whatsapp: number.isWhatsapp,
        created_at: number.createdAt,
        updated_at: number.updatedAt,
      }));
    } catch (error) {
      console.error('Error getting all numbers:', error);
      return [];
    }
  }

  async getByCompanyId(companyId: number): Promise<NumberData[]> {
    try {
      const numbers = await this.collection
        .query(Q.where('company_id', companyId))
        .fetch();
      
      return numbers.map(number => ({
        id: number.id,
        api_number_id: number.apiNumberId,
        company_id: number.companyId,
        number: number.number,
        description: number.description,
        type: number.type,
        upvote_count: number.upvoteCount,
        downvote_count: number.downvoteCount,
        is_whatsapp: number.isWhatsapp,
        created_at: number.createdAt,
        updated_at: number.updatedAt,
      }));
    } catch (error) {
      console.error('Error getting numbers by company ID:', error);
      return [];
    }
  }

  async getByCompanyIdGrouped(companyId: number): Promise<{
    TOLL_FREE: NumberData[];
    ALL_INDIA: NumberData[];
    INTERNATIONAL: NumberData[];
  }> {
    try {
      const numbers = await this.getByCompanyId(companyId);
      
      return {
        TOLL_FREE: numbers.filter(num => num.type === 'TOLL_FREE'),
        ALL_INDIA: numbers.filter(num => num.type === 'ALL_INDIA'),
        INTERNATIONAL: numbers.filter(num => num.type === 'INTERNATIONAL'),
      };
    } catch (error) {
      console.error('Error getting grouped numbers by company ID:', error);
      return {
        TOLL_FREE: [],
        ALL_INDIA: [],
        INTERNATIONAL: [],
      };
    }
  }

  async create(numberData: NumberData): Promise<string> {
    try {
      const newNumber = await database.write(async () => {
        return await this.collection.create(number => {
          number.apiNumberId = numberData.api_number_id || 0;
          number.companyId = numberData.company_id;
          number.number = numberData.number;
          number.description = numberData.description || '';
          number.type = numberData.type;
          number.upvoteCount = numberData.upvote_count || 0;
          number.downvoteCount = numberData.downvote_count || 0;
          number.isWhatsapp = numberData.is_whatsapp || 0;
        });
      });
      return newNumber.id;
    } catch (error) {
      console.error('Error creating number:', error);
      throw error;
    }
  }

  async batchCreate(numbersData: NumberData[]): Promise<void> {
    try {
      const maxBatchSize = 100; // Process in smaller batches to avoid memory issues

      if (numbersData.length <= maxBatchSize) {
        // Single batch
        await database.write(async () => {
          const batch = numbersData.map(numberData =>
            this.collection.prepareCreate(number => {
              number.apiNumberId = numberData.api_number_id || 0;
              number.companyId = numberData.company_id;
              number.number = numberData.number;
              number.description = numberData.description || '';
              number.type = numberData.type;
              number.upvoteCount = numberData.upvote_count || 0;
              number.downvoteCount = numberData.downvote_count || 0;
              number.isWhatsapp = numberData.is_whatsapp || 0;
            })
          );
          await database.batch(...batch);
        });
      } else {
        // Multiple batches
        for (let i = 0; i < numbersData.length; i += maxBatchSize) {
          const chunk = numbersData.slice(i, i + maxBatchSize);
          await database.write(async () => {
            const batch = chunk.map(numberData =>
              this.collection.prepareCreate(number => {
                number.apiNumberId = numberData.api_number_id || 0;
                number.companyId = numberData.company_id;
                number.number = numberData.number;
                number.description = numberData.description || '';
                number.type = numberData.type;
                number.upvoteCount = numberData.upvote_count || 0;
                number.downvoteCount = numberData.downvote_count || 0;
                number.isWhatsapp = numberData.is_whatsapp || 0;
              })
            );
            await database.batch(...batch);
          });
        }
      }
    } catch (error) {
      console.error('Error batch creating numbers:', error);
      throw error;
    }
  }

  async clearAll(): Promise<void> {
    try {
      await database.write(async () => {
        const allNumbers = await this.collection.query().fetch();
        const batch = allNumbers.map(number => number.prepareDestroyPermanently());
        await database.batch(...batch);
      });
      console.log('All numbers cleared from database');
    } catch (error) {
      console.error('Error clearing all numbers:', error);
      throw error;
    }
  }

  async getCount(): Promise<number> {
    try {
      return await this.collection.query().fetchCount();
    } catch (error) {
      console.error('Error getting numbers count:', error);
      return 0;
    }
  }

  async updateVoteCounts(numberId: number, upvoteCount: number, downvoteCount: number): Promise<boolean> {
    try {
      const number = await this.collection
        .query(Q.where('api_number_id', numberId))
        .fetch();

      if (number.length === 0) {
        console.warn(`Number with API ID ${numberId} not found in local database`);
        return false;
      }

      await database.write(async () => {
        await number[0].update(numberRecord => {
          numberRecord.upvoteCount = upvoteCount;
          numberRecord.downvoteCount = downvoteCount;
        });
      });

      console.log(`Updated vote counts for number ${numberId}: ${upvoteCount} up, ${downvoteCount} down`);
      return true;
    } catch (error) {
      console.error('Error updating vote counts:', error);
      return false;
    }
  }

  async updateVoteCountsByNumber(phoneNumber: string, upvoteCount: number, downvoteCount: number): Promise<boolean> {
    try {
      const number = await this.collection
        .query(Q.where('number', phoneNumber))
        .fetch();

      if (number.length === 0) {
        console.warn(`Number ${phoneNumber} not found in local database`);
        return false;
      }

      await database.write(async () => {
        await number[0].update(numberRecord => {
          numberRecord.upvoteCount = upvoteCount;
          numberRecord.downvoteCount = downvoteCount;
        });
      });

      console.log(`Updated vote counts for number ${phoneNumber}: ${upvoteCount} up, ${downvoteCount} down`);
      return true;
    } catch (error) {
      console.error('Error updating vote counts by number:', error);
      return false;
    }
  }

  async incrementUpvote(numberId: number): Promise<boolean> {
    try {
      const number = await this.collection
        .query(Q.where('api_number_id', numberId))
        .fetch();

      if (number.length === 0) {
        console.warn(`Number with API ID ${numberId} not found in local database`);
        return false;
      }

      await database.write(async () => {
        await number[0].update(numberRecord => {
          numberRecord.upvoteCount = numberRecord.upvoteCount + 1;
        });
      });

      console.log(`Incremented upvote for number ${numberId}`);
      return true;
    } catch (error) {
      console.error('Error incrementing upvote:', error);
      return false;
    }
  }

  async incrementDownvote(numberId: number): Promise<boolean> {
    try {
      const number = await this.collection
        .query(Q.where('api_number_id', numberId))
        .fetch();

      if (number.length === 0) {
        console.warn(`Number with API ID ${numberId} not found in local database`);
        return false;
      }

      await database.write(async () => {
        await number[0].update(numberRecord => {
          numberRecord.downvoteCount = numberRecord.downvoteCount + 1;
        });
      });

      console.log(`Incremented downvote for number ${numberId}`);
      return true;
    } catch (error) {
      console.error('Error incrementing downvote:', error);
      return false;
    }
  }
}

export default new WatermelonNumberRepository();
