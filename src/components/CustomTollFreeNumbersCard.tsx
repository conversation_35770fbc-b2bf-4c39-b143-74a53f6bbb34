import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Linking,
  Alert,
} from 'react-native';
import {Images} from '../assets';

import {ContactNumber} from '../screens/companyDetails/companyDetailsScreen';
import WebSocketVoteService from '../socket/iccappush.js';

interface Props {
  numberData?: ContactNumber;
  onVoteUpdate?: (numberId: number, isUpvote: boolean) => Promise<void>;
}

const CustomTollFreeNumbersCard = ({numberData, onVoteUpdate}: Props) => {
  const [isVoting, setIsVoting] = useState(false);
  // Local state for vote counts to enable real-time updates
  const [localUpvoteCount, setLocalUpvoteCount] = useState(
    numberData?.upvoteCount || 0,
  );
  const [localDownvoteCount, setLocalDownvoteCount] = useState(
    numberData?.downvoteCount || 0,
  );

  // Sync local state when numberData changes
  useEffect(() => {
    setLocalUpvoteCount(numberData?.upvoteCount || 0);
    setLocalDownvoteCount(numberData?.downvoteCount || 0);
  }, [numberData?.upvoteCount, numberData?.downvoteCount]);

  const handlePhoneCall = (number: string) => {
    const phoneNumber = `tel:${number}`;
    Linking.openURL(phoneNumber).catch((err: Error) =>
      console.error('Failed to open phone call:', err),
    );
  };

  const handleUpVote = async () => {
    if (isVoting || !numberData?.number) return;

    setIsVoting(true);
    try {
      console.log('👍 User tapped upvote for phone number:', numberData.number);
      const result = await WebSocketVoteService.sendVote(
        numberData.number,
        true,
      );

      // If WebSocket vote was successful, increment local upvote count
      if (result && result.success) {
        setLocalUpvoteCount(prevCount => prevCount + 1);
        console.log('✅ Upvote successful, incremented local count');

        // Update local database through callback
        if (onVoteUpdate && numberData?.numberId) {
          try {
            await onVoteUpdate(numberData.numberId, true);
            console.log('✅ Local database updated for upvote');
          } catch (error) {
            console.error(
              '❌ Failed to update local database for upvote:',
              error,
            );
          }
        }

        // Show different messages based on confirmation status
        if (result.confirmed) {
          Alert.alert(
            'Success',
            'Your upvote has been confirmed by the server!',
          );
        } else if (result.timeout) {
          Alert.alert(
            'Success',
            'Your upvote has been sent! (Server confirmation pending)',
          );
        } else {
          Alert.alert('Success', 'Your upvote has been recorded!');
        }
      }
    } catch (error) {
      console.error('❌ Failed to send upvote:', error);

      // Show different messages based on error type
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('timeout')) {
        Alert.alert(
          'Connection Timeout',
          'The server is taking too long to respond. Your vote has been queued and will be sent when connection is restored.',
        );
      } else if (errorMessage.includes('Connection failed')) {
        Alert.alert(
          'Connection Error',
          'Unable to connect to the voting server. Your vote has been queued and will be sent when connection is restored.',
        );
      } else {
        Alert.alert('Error', 'Failed to record your vote. Please try again.');
      }
    } finally {
      setIsVoting(false);
    }
  };

  const handleDownVote = async () => {
    if (isVoting || !numberData?.number) return;

    setIsVoting(true);
    try {
      console.log(
        '👎 User tapped downvote for phone number:',
        numberData.number,
      );
      const result = await WebSocketVoteService.sendVote(
        numberData.number,
        false,
      );

      // If WebSocket vote was successful, increment local downvote count
      if (result && result.success) {
        setLocalDownvoteCount(prevCount => prevCount + 1);
        console.log('✅ Downvote successful, incremented local count');

        // Update local database through callback
        if (onVoteUpdate && numberData?.numberId) {
          try {
            await onVoteUpdate(numberData.numberId, false);
            console.log('✅ Local database updated for downvote');
          } catch (error) {
            console.error(
              '❌ Failed to update local database for downvote:',
              error,
            );
          }
        }

        // Show different messages based on confirmation status
        if (result.confirmed) {
          Alert.alert(
            'Success',
            'Your downvote has been confirmed by the server!',
          );
        } else if (result.timeout) {
          Alert.alert(
            'Success',
            'Your downvote has been sent! (Server confirmation pending)',
          );
        } else {
          Alert.alert('Success', 'Your downvote has been recorded!');
        }
      }
    } catch (error) {
      console.error('❌ Failed to send downvote:', error);

      // Show different messages based on error type
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('timeout')) {
        Alert.alert(
          'Connection Timeout',
          'The server is taking too long to respond. Your vote has been queued and will be sent when connection is restored.',
        );
      } else if (errorMessage.includes('Connection failed')) {
        Alert.alert(
          'Connection Error',
          'Unable to connect to the voting server. Your vote has been queued and will be sent when connection is restored.',
        );
      } else {
        Alert.alert('Error', 'Failed to record your vote. Please try again.');
      }
    } finally {
      setIsVoting(false);
    }
  };

  const formatVoteCount = (count: number): string => {
    if (count < 1000) {
      return count.toString();
    }
    return (count / 1000).toFixed(1) + 'k';
  };
  return (
    <View style={styles.card}>
      <View style={styles.titleView}>
        <Text style={[styles.titleText]}>{numberData?.description}</Text>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'center',
          }}>
          <Image style={styles.phoneIcon} source={Images.ic_phoneCall} />
          <TouchableOpacity
            onPress={() => handlePhoneCall(numberData?.number ?? '')}>
            <Text style={[styles.phoneNumber]}>{numberData?.number}</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.generalInfo}>
          <TouchableOpacity
            onPress={handleUpVote}
            disabled={isVoting}
            style={[
              styles.iconContainer,
              isVoting && styles.disabledContainer,
            ]}>
            <Image style={styles.upVoteIcon} source={Images.ic_arrowUp} />
            <Text style={styles.upVoteText}>
              {' '}
              {formatVoteCount(localUpvoteCount)}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handleDownVote}
            disabled={isVoting}
            style={[
              styles.iconContainer,
              isVoting && styles.disabledContainer,
            ]}>
            <Image style={styles.upVoteIcon} source={Images.ic_arrowDown} />
            <Text style={styles.upVoteText}>
              {formatVoteCount(localDownvoteCount)}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    flexDirection: 'column',
    alignItems: 'stretch',
    backgroundColor: '#f1f5f9',
    borderRadius: 10,
    marginBottom: 10,
    borderWidth: 1.5,
    borderColor: '#D7E2F1',
    justifyContent: 'space-evenly',
  },
  titleText: {
    fontFamily: 'Poppins-Bold',
    fontSize: 17,
  },
  titleView: {
    margin: 10,
  },
  phoneNumber: {
    fontSize: 17,
    fontFamily: 'Poppins-Medium',
    textDecorationLine: 'underline',
    color: '#0066cc',
  },
  phoneIcon: {
    marginBottom: 3,
    marginLeft: 3,
    height: 20,
    width: 20,
    marginRight: 5,
  },
  generalInfo: {
    paddingTop: 5,
    flexDirection: 'row',
    gap: 10,
    alignItems: 'center',
    width: 200,
  },
  iconContainer: {
    backgroundColor: '#D9E2EF',
    padding: 4,
    borderRadius: 6,
    width: 60,
    height: 30,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  upVoteIcon: {
    marginLeft: 3,
    height: 20,
    width: 20,
    marginRight: 5,
  },
  upVoteText: {
    marginTop: 1,
    fontSize: 14,
    fontFamily: 'Poppins-Medium',
    marginRight: 5,
  },
  disabledContainer: {
    opacity: 0.5,
  },
});

export default CustomTollFreeNumbersCard;
